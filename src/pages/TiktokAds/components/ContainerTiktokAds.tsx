import { useEffect, useState } from 'react';
import { TFilterAudienceTiktok } from '@/types/facebook';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import CustomTiktokAudiences from '@/pages/TiktokAds/views/CustomTiktokAudiences';
import HeaderTik<PERSON> from '@/pages/TiktokAds/components/HeaderTiktok';
import { ICustomAudienceResponse } from '@/types/tiktok';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { QUERY_KEY } from '@/utils/constants';
import { useQuery } from '@tanstack/react-query';

const ContainerTiktokAds = () => {
  const [filterPayload, setFilterPayload] = useState<TFilterAudienceTiktok>({
    search: '',
    page: 1,
    limit: 10,
    date_created_from: '',
    date_created_to: '',
    platform: 'TT',
  });
  const { adsAccount } = useTiktokContext();
  const [tiktokCustomAudience, setTiktokCustomAudience] = useState<ICustomAudienceResponse>({
    items: [],
    count: 0,
  });

  const { data: customAudienceResponse, isLoading: loadingContact } = useQuery({
    queryKey: [QUERY_KEY.TIKTOK_AUDIENCE, filterPayload, adsAccount?.ad_account_id],
    enabled: !!adsAccount?.ad_account_id,
    staleTime: 1000,
    queryFn: () =>
      get({
        endpoint: ENDPOINTS.custom_audience[''],
        params: {
          ...filterPayload,
          ad_account_id: adsAccount?.ad_account_id,
        },
      }).then((res) => {
        if (res?.data?.code === 1001) {
          return {
            items: [],
            count: 0,
          };
        }
        return res.data?.data as ICustomAudienceResponse;
      }),
  });

  useEffect(() => {
    if (!!customAudienceResponse) {
      setTiktokCustomAudience(customAudienceResponse);
    }
  }, [customAudienceResponse]);

  return (
    <>
      <HeaderTiktok
        filterPayload={filterPayload}
        setFilterPayload={setFilterPayload}
        ad_Account_id={adsAccount?.ad_account_id ?? ''}
      />

      <CustomTiktokAudiences
        filterPayload={filterPayload}
        setFilterPayload={setFilterPayload}
        loading={loadingContact}
        tiktokCustomAudience={tiktokCustomAudience}
        setTiktokCustomAudience={setTiktokCustomAudience}
      />
    </>
  );
};

export default ContainerTiktokAds;
