describe('Update TikTok Audience', () => {
  beforeEach(() => {
    cy.mockTikTokAPI();
    cy.loginTikTok();
    cy.visit('/tiktok-ads');
    cy.waitForTikTokData();
  });

  describe('Update Modal Display', () => {
    it('should open update modal when clicking update button', () => {
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="update-audience-modal"]').should('be.visible');
      cy.get('[data-testid="modal-title"]').should('contain', 'Update Audience');
    });

    it('should display audience name in disabled input', () => {
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="audience-name-input"]')
        .should('be.disabled')
        .should('have.value', 'Test Audience 1');
    });

    it('should display history section', () => {
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="history-section"]').should('be.visible');
      cy.get('[data-testid="history-title"]').should('contain', 'Included');
    });

    it('should display segment selector', () => {
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="segment-selector"]').should('be.visible');
    });
  });

  describe('History Display', () => {
    it('should load and display history items', () => {
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.wait('@getHistory');
      cy.get('[data-testid="history-item"]').should('have.length.at.least', 1);
      cy.get('[data-testid="history-item"]').first().should('contain', 'Test Segment');
    });

    it('should handle infinite scroll in history', () => {
      // Mock paginated history response
      cy.intercept('GET', '**/api/custom-audience/history/*', (req) => {
        const page = req.query.page || 1;
        req.reply({
          statusCode: 200,
          body: {
            count: 50,
            items: Array.from({ length: 20 }, (_, i) => ({
              id: i + 1 + (page - 1) * 20,
              segment_info: { name: `Segment ${i + 1 + (page - 1) * 20}` },
              created_at: '2024-01-01T00:00:00Z'
            }))
          }
        });
      }).as('getPaginatedHistory');

      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.wait('@getPaginatedHistory');
      
      // Scroll to bottom to trigger next page
      cy.get('[data-testid="history-container"]').scrollTo('bottom');
      cy.wait('@getPaginatedHistory');
      
      cy.get('[data-testid="history-item"]').should('have.length', 40);
    });

    it('should show loading state while fetching history', () => {
      cy.intercept('GET', '**/api/custom-audience/history/*', (req) => {
        req.reply((res) => {
          res.delay(1000);
          res.send({
            statusCode: 200,
            body: cy.fixture('tiktok-data').then(data => data.history)
          });
        });
      }).as('slowHistory');

      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="history-loading"]').should('be.visible');
      cy.wait('@slowHistory');
      cy.get('[data-testid="history-loading"]').should('not.exist');
    });
  });

  describe('Time Validation Logic', () => {
    it('should allow update when audience was updated more than 1 hour ago', () => {
      // Mock audience with old updated_at timestamp
      const oldTimestamp = new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(); // 2 hours ago
      
      cy.intercept('GET', '**/api/tiktok/custom-audiences*', {
        statusCode: 200,
        body: {
          count: 1,
          items: [{
            id: 1,
            audience_name: 'Old Audience',
            audience_size: 1000,
            status: 'ACTIVE',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: oldTimestamp,
            job_id: 101
          }]
        }
      }).as('getOldAudience');

      cy.reload();
      cy.wait('@getOldAudience');

      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="segment-selector"]').click();
      cy.get('[data-testid="segment-option-1"]').click();
      
      cy.get('[data-testid="update-audience-button"]').click();
      
      cy.wait('@updateAudience');
      cy.get('[data-testid="success-toast"]').should('be.visible');
      cy.get('[data-testid="update-audience-modal"]').should('not.exist');
    });

    it('should show limit warning when audience was updated less than 1 hour ago', () => {
      // Mock audience with recent updated_at timestamp
      const recentTimestamp = new Date(Date.now() - 30 * 60 * 1000).toISOString(); // 30 minutes ago
      
      cy.intercept('GET', '**/api/tiktok/custom-audiences*', {
        statusCode: 200,
        body: {
          count: 1,
          items: [{
            id: 1,
            audience_name: 'Recent Audience',
            audience_size: 1000,
            status: 'ACTIVE',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: recentTimestamp,
            job_id: 101
          }]
        }
      }).as('getRecentAudience');

      cy.reload();
      cy.wait('@getRecentAudience');

      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="segment-selector"]').click();
      cy.get('[data-testid="segment-option-1"]').click();
      
      cy.get('[data-testid="update-audience-button"]').click();
      
      // Should show limit warning modal and close update modal
      cy.get('[data-testid="limit-update-modal"]').should('be.visible');
      cy.get('[data-testid="update-audience-modal"]').should('not.exist');
    });

    it('should close limit warning modal', () => {
      const recentTimestamp = new Date(Date.now() - 30 * 60 * 1000).toISOString();
      
      cy.intercept('GET', '**/api/tiktok/custom-audiences*', {
        statusCode: 200,
        body: {
          count: 1,
          items: [{
            id: 1,
            audience_name: 'Recent Audience',
            audience_size: 1000,
            status: 'ACTIVE',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: recentTimestamp,
            job_id: 101
          }]
        }
      }).as('getRecentAudience');

      cy.reload();
      cy.wait('@getRecentAudience');

      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="segment-selector"]').click();
      cy.get('[data-testid="segment-option-1"]').click();
      cy.get('[data-testid="update-audience-button"]').click();
      
      cy.get('[data-testid="limit-update-modal"]').should('be.visible');
      cy.get('[data-testid="close-limit-modal"]').click();
      cy.get('[data-testid="limit-update-modal"]').should('not.exist');
    });
  });

  describe('Segment Selection', () => {
    it('should open segment selector dropdown', () => {
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="segment-selector"]').click();
      cy.get('[data-testid="segment-dropdown"]').should('be.visible');
    });

    it('should select a segment', () => {
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="segment-selector"]').click();
      cy.get('[data-testid="segment-option-1"]').click();
      
      cy.get('[data-testid="segment-selector"]').should('contain', 'Test Segment 1');
    });

    it('should enable update button when segment is selected', () => {
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="update-audience-button"]').should('be.disabled');
      
      cy.get('[data-testid="segment-selector"]').click();
      cy.get('[data-testid="segment-option-1"]').click();
      
      cy.get('[data-testid="update-audience-button"]').should('not.be.disabled');
    });
  });

  describe('Modal Actions', () => {
    it('should close modal when clicking cancel', () => {
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="cancel-update-button"]').click();
      cy.get('[data-testid="update-audience-modal"]').should('not.exist');
    });

    it('should show loading state during update', () => {
      cy.intercept('POST', '**/api/tiktok/large-update', (req) => {
        req.reply((res) => {
          res.delay(1000);
          res.send({ statusCode: 200, body: { success: true } });
        });
      }).as('slowUpdate');

      const oldTimestamp = new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString();
      
      cy.intercept('GET', '**/api/tiktok/custom-audiences*', {
        statusCode: 200,
        body: {
          count: 1,
          items: [{
            id: 1,
            audience_name: 'Old Audience',
            updated_at: oldTimestamp,
            job_id: 101
          }]
        }
      }).as('getOldAudience');

      cy.reload();
      cy.wait('@getOldAudience');

      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="segment-selector"]').click();
      cy.get('[data-testid="segment-option-1"]').click();
      cy.get('[data-testid="update-audience-button"]').click();
      
      cy.get('[data-testid="update-audience-button"]').should('be.disabled');
      cy.get('[data-testid="loading-spinner"]').should('be.visible');
      
      cy.wait('@slowUpdate');
      cy.get('[data-testid="success-toast"]').should('be.visible');
    });
  });

  describe('Error Handling', () => {
    it('should handle update API error', () => {
      cy.intercept('POST', '**/api/tiktok/large-update', {
        statusCode: 400,
        body: { error: 'Update failed' }
      }).as('updateError');

      const oldTimestamp = new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString();
      
      cy.intercept('GET', '**/api/tiktok/custom-audiences*', {
        statusCode: 200,
        body: {
          count: 1,
          items: [{
            id: 1,
            audience_name: 'Old Audience',
            updated_at: oldTimestamp,
            job_id: 101
          }]
        }
      }).as('getOldAudience');

      cy.reload();
      cy.wait('@getOldAudience');

      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="segment-selector"]').click();
      cy.get('[data-testid="segment-option-1"]').click();
      cy.get('[data-testid="update-audience-button"]').click();
      
      cy.wait('@updateError');
      cy.get('[data-testid="error-toast"]').should('be.visible');
    });

    it('should handle history loading error', () => {
      cy.intercept('GET', '**/api/custom-audience/history/*', {
        statusCode: 500,
        body: { error: 'Failed to load history' }
      }).as('historyError');

      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.wait('@historyError');
      cy.get('[data-testid="history-error"]').should('be.visible');
    });
  });

  describe('Data Refresh After Update', () => {
    it('should refresh table data after successful update', () => {
      const oldTimestamp = new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString();

      // Initial data with old status
      cy.intercept('GET', '**/api/v1/custom-audience/jobs*', {
        statusCode: 200,
        body: {
          count: 1,
          items: [{
            id: 1,
            audience_name: 'Test Audience',
            audience_id: 'aud_123',
            status: 'COMPLETED',
            total_records: 1000,
            updated_at: oldTimestamp,
            job_id: 101,
            segment_name: 'Test Segment'
          }]
        }
      }).as('getInitialAudiences');

      // Updated data after successful update
      cy.intercept('GET', '**/api/v1/custom-audience/jobs*', {
        statusCode: 200,
        body: {
          count: 1,
          items: [{
            id: 1,
            audience_name: 'Test Audience',
            audience_id: 'aud_123',
            status: 'PROCESSING',
            total_records: 1500,
            updated_at: new Date().toISOString(),
            job_id: 101,
            segment_name: 'Updated Segment'
          }]
        }
      }).as('getUpdatedAudiences');

      cy.intercept('POST', '**/api/tiktok/large-update', {
        statusCode: 200,
        body: { success: true }
      }).as('updateSuccess');

      cy.reload();
      cy.wait('@getInitialAudiences');

      // Verify initial data
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.contains('1,000').should('be.visible');
        cy.get('[data-testid="update-button"]').click();
      });

      cy.get('[data-testid="segment-selector"]').click();
      cy.get('[data-testid="segment-option-1"]').click();
      cy.get('[data-testid="update-audience-button"]').click();

      cy.wait('@updateSuccess');
      cy.wait('@getUpdatedAudiences');

      // Verify data has been refreshed
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.contains('1,500').should('be.visible');
        cy.contains('PROCESSING').should('be.visible');
      });
    });

    it('should update individual row data when idActive changes', () => {
      cy.intercept('GET', '**/api/v1/custom-audience/jobs*', {
        statusCode: 200,
        body: {
          count: 2,
          items: [{
            id: 1,
            audience_name: 'Pending Audience',
            status: 'PENDING',
            total_records: 0,
            job_id: 101
          }, {
            id: 2,
            audience_name: 'Completed Audience',
            status: 'COMPLETED',
            total_records: 1000,
            job_id: 102
          }]
        }
      }).as('getAudiences');

      cy.intercept('GET', '**/api/v1/custom-audience/jobs/101', {
        statusCode: 200,
        body: {
          data: {
            id: 1,
            audience_name: 'Pending Audience',
            status: 'COMPLETED',
            total_records: 500,
            job_id: 101
          }
        }
      }).as('getAudienceDetail');

      cy.reload();
      cy.wait('@getAudiences');

      // Click on pending audience to trigger detail fetch
      cy.get('[data-testid="audience-row"]').first().click();
      cy.wait('@getAudienceDetail');

      // Verify the row data has been updated
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.contains('500').should('be.visible');
        cy.contains('COMPLETED').should('be.visible');
      });
    });
  });
});
